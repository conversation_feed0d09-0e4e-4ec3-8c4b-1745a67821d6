1753100231O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:6:{i:0;O:17:"App\Models\Slider":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:7:"sliders";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:7;s:12:"slider_image";s:38:"slider-images/1727935696_Q3 winner.jpg";s:10:"created_at";s:19:"2024-10-03 06:08:16";s:10:"updated_at";s:19:"2024-10-03 06:08:16";}s:11:" * original";a:4:{s:2:"id";i:7;s:12:"slider_image";s:38:"slider-images/1727935696_Q3 winner.jpg";s:10:"created_at";s:19:"2024-10-03 06:08:16";s:10:"updated_at";s:19:"2024-10-03 06:08:16";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:12:"slider_image";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:1;O:17:"App\Models\Slider":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:7:"sliders";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:6;s:12:"slider_image";s:48:"slider-images/1725607973_Revnue  data for Q3.png";s:10:"created_at";s:19:"2024-09-06 07:32:53";s:10:"updated_at";s:19:"2024-09-06 07:32:53";}s:11:" * original";a:4:{s:2:"id";i:6;s:12:"slider_image";s:48:"slider-images/1725607973_Revnue  data for Q3.png";s:10:"created_at";s:19:"2024-09-06 07:32:53";s:10:"updated_at";s:19:"2024-09-06 07:32:53";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:12:"slider_image";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:2;O:17:"App\Models\Slider":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:7:"sliders";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:5;s:12:"slider_image";s:53:"slider-images/1720439449_revenue 2024 JULY-01 (1).jpg";s:10:"created_at";s:19:"2024-07-08 11:50:49";s:10:"updated_at";s:19:"2024-07-08 11:50:49";}s:11:" * original";a:4:{s:2:"id";i:5;s:12:"slider_image";s:53:"slider-images/1720439449_revenue 2024 JULY-01 (1).jpg";s:10:"created_at";s:19:"2024-07-08 11:50:49";s:10:"updated_at";s:19:"2024-07-08 11:50:49";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:12:"slider_image";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:3;O:17:"App\Models\Slider":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:7:"sliders";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:4;s:12:"slider_image";s:38:"slider-images/1717148368_image (5).png";s:10:"created_at";s:19:"2024-05-31 09:39:28";s:10:"updated_at";s:19:"2024-05-31 09:39:28";}s:11:" * original";a:4:{s:2:"id";i:4;s:12:"slider_image";s:38:"slider-images/1717148368_image (5).png";s:10:"created_at";s:19:"2024-05-31 09:39:28";s:10:"updated_at";s:19:"2024-05-31 09:39:28";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:12:"slider_image";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:4;O:17:"App\Models\Slider":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:7:"sliders";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:2;s:12:"slider_image";s:37:"slider-images/1717081394_odz-logo.jpg";s:10:"created_at";s:19:"2024-05-30 15:03:14";s:10:"updated_at";s:19:"2024-05-30 15:03:14";}s:11:" * original";a:4:{s:2:"id";i:2;s:12:"slider_image";s:37:"slider-images/1717081394_odz-logo.jpg";s:10:"created_at";s:19:"2024-05-30 15:03:14";s:10:"updated_at";s:19:"2024-05-30 15:03:14";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:12:"slider_image";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:5;O:17:"App\Models\Slider":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:7:"sliders";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:1;s:12:"slider_image";s:38:"slider-images/1717081369_login-img.jpg";s:10:"created_at";s:19:"2024-05-30 15:02:49";s:10:"updated_at";s:19:"2024-05-30 15:02:49";}s:11:" * original";a:4:{s:2:"id";i:1;s:12:"slider_image";s:38:"slider-images/1717081369_login-img.jpg";s:10:"created_at";s:19:"2024-05-30 15:02:49";s:10:"updated_at";s:19:"2024-05-30 15:02:49";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:12:"slider_image";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}