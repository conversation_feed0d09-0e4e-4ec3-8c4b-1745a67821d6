<?php if (isset($component)) { $__componentOriginalf56340035eeb7de26edb3ee88689cd6b = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf56340035eeb7de26edb3ee88689cd6b = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.user.app','data' => ['title' => $page_title,'fromUrl' => route('dashboard')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('user.app'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($page_title),'from_url' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('dashboard'))]); ?>

     <?php $__env->slot('breadcrumbSlot', null, []); ?> 
        <a href="<?php echo e(route('cv.create')); ?>" class="btn btn-primary">
            <i class="fa fa-plus"></i>
            <span>Add new cv</span>
        </a>
     <?php $__env->endSlot(); ?>

    <div class="card">
        <div class="card-header">
            <h4 class="card-title">
                <div class="row justify-content-end">
                    <div class="col-md-3">
                        <div class="form-group">
                            <input type="text" class="form-control" placeholder="Search..." name="search"
                                   id="searchInput">
                        </div>
                    </div>
                </div>
            </h4>

        </div>
        <div class="card-body">
            <div class="table-responsive" id="data-container">
                <table class="table table-bordered table-striped table-hover" id="video-table">
                    <thead>
                    <tr>
                        <th>SL#</th>
                        <th>Upwork ID</th>
                        <th>Title</th>
                        <th class="text-center">Action</th>
                    </tr>
                    </thead>
                    <tbody>
                    <?php $__empty_1 = true; $__currentLoopData = $cvs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cv): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr>
                            <td><?php echo e($cvs->firstItem() + $loop->index); ?></td>
                            <td>
                                <?php echo e($cv->upworkID->upwork_id); ?>

                                <?php if(isset($cv->other_values['upwork_id_other'])): ?>
                                    <?php echo e(' - ' . @$cv->other_values['upwork_id_other']); ?>

                                <?php endif; ?>
                            </td>
                            <td><?php echo e($cv->title); ?></td>
                            <td class="text-center">
                                <a href="<?php echo e(route('cv.edit', $cv->id)); ?>" class="btn btn-secondary mb-2">
                                    <i class="fa fa-edit"></i>
                                </a>
                                <a href="<?php echo e(route('cv.show', $cv->id)); ?>" class="btn btn-info mb-2">
                                    <i class="fa fa-eye"></i>
                                </a>
                                <a href="<?php echo e(route('cv.destroy', $cv->id)); ?>" class="btn btn-danger mb-2"
                                   onclick="return makeDeleteRequest(event, <?php echo e($cv->id); ?>)">
                                    <i class="fa fa-trash"></i>
                                    <?php if (isset($component)) { $__componentOriginalf9a5f060e1fbbcbc7beb643b113b10ab = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf9a5f060e1fbbcbc7beb643b113b10ab = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form','data' => ['action' => ''.e(route('cv.destroy', $cv->id)).'','id' => 'delete-form-'.e($cv->id).'','style' => 'display: none;','method' => 'DELETE']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['action' => ''.e(route('cv.destroy', $cv->id)).'','id' => 'delete-form-'.e($cv->id).'','style' => 'display: none;','method' => 'DELETE']); ?>
                                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf9a5f060e1fbbcbc7beb643b113b10ab)): ?>
<?php $attributes = $__attributesOriginalf9a5f060e1fbbcbc7beb643b113b10ab; ?>
<?php unset($__attributesOriginalf9a5f060e1fbbcbc7beb643b113b10ab); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf9a5f060e1fbbcbc7beb643b113b10ab)): ?>
<?php $component = $__componentOriginalf9a5f060e1fbbcbc7beb643b113b10ab; ?>
<?php unset($__componentOriginalf9a5f060e1fbbcbc7beb643b113b10ab); ?>
<?php endif; ?>
                                </a>
                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td class="text-center" colspan="100%">
                                No cv found!
                            </td>
                        </tr>
                    <?php endif; ?>
                    </tbody>

                    <?php if(count($cvs)): ?>
                        <tfoot>
                        <tr>
                            <td colspan="100%">
                                <div class="float-right mt-3 mr-3" id="pagination-links">
                                    <?php echo e($cvs->withQueryString()->links()); ?>

                                </div>
                            </td>
                        </tr>
                        </tfoot>
                    <?php endif; ?>
                </table>
            </div>
        </div>
    </div>

     <?php $__env->slot('bottomScripts', null, []); ?> 
        <script type="text/javascript">
            function fetchData(data) {
                $.ajax({
                    url    : '<?php echo e(route('cv.index')); ?>',
                    type   : 'GET',
                    data   : data,
                    success: function (response) {
                        $('#data-container').html(response.data);
                    },
                    error  : function (xhr) {
                        console.log(xhr.responseText);
                    }
                });
            }

            $(document).ready(function () {
                $(document).on('click', '#pagination-links a', function (e) {
                    e.preventDefault();

                    let page = $(this).attr('href').split('page=')[1];
                    let search = $('#searchInput').val();
                    const data = {
                        page  : page,
                        search: search
                    };

                    fetchData(data);
                });

                $('#searchInput').on('input', function () {
                    const data = {
                        page  : 1,
                        search: $(this).val()
                    };

                    fetchData(data);
                });
            });
        </script>
     <?php $__env->endSlot(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf56340035eeb7de26edb3ee88689cd6b)): ?>
<?php $attributes = $__attributesOriginalf56340035eeb7de26edb3ee88689cd6b; ?>
<?php unset($__attributesOriginalf56340035eeb7de26edb3ee88689cd6b); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf56340035eeb7de26edb3ee88689cd6b)): ?>
<?php $component = $__componentOriginalf56340035eeb7de26edb3ee88689cd6b; ?>
<?php unset($__componentOriginalf56340035eeb7de26edb3ee88689cd6b); ?>
<?php endif; ?>
<?php /**PATH D:\xampp8.2\htdocs\portfolio\resources\views/user/cv/index.blade.php ENDPATH**/ ?>