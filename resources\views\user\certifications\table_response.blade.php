<table class="table table-bordered table-striped table-hover" id="certification-table">
    <thead>
    <tr>
        <th>SL#</th>
        <th>Certification Title</th>
        <th>Upwork ID</th>
        <th>Certification Date</th>
        <th>File</th>
        <th class="text-center">Action</th>
    </tr>
    </thead>
    <tbody>
    @forelse($certifications as $certification)
        <tr>
            <td>{{ $certifications->firstItem() + $loop->index }}</td>
            <td>{{ $certification->certification_title ?? 'N/A' }}</td>
            <td>{{ $certification->upworkID->upwork_id ?? 'N/A' }}</td>
            <td>{{ $certification->certification_date ? $certification->certification_date->format('M d, Y') : 'N/A' }}</td>
            <td>
                @if($certification->certification_upload)
                    <a href="{{ Storage::url($certification->certification_upload) }}" target="_blank" class="btn btn-sm btn-info">
                        <i class="fa fa-download"></i> View
                    </a>
                @else
                    N/A
                @endif
            </td>
            <td class="text-center">
                <div class="btn-group">
                    <a href="{{ route('certifications.show', $certification->id) }}" class="btn btn-sm btn-info">
                        <i class="fa fa-eye"></i>
                    </a>
                    <a href="{{ route('certifications.edit', $certification->id) }}" class="btn btn-sm btn-warning">
                        <i class="fa fa-edit"></i>
                    </a>
                    <form action="{{ route('certifications.destroy', $certification->id) }}" method="POST" style="display: inline-block;">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this certification?')">
                            <i class="fa fa-trash"></i>
                        </button>
                    </form>
                </div>
            </td>
        </tr>
    @empty
        <tr>
            <td colspan="6" class="text-center">No certifications found.</td>
        </tr>
    @endforelse
    </tbody>
</table>

<div class="d-flex justify-content-center">
    {{ $certifications->links() }}
</div>
