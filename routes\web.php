<?php

use App\Http\Controllers\User\{Communication\ToolsController as UserToolsController,
    DashboardController as UserDashboardController,
    Portfolio\PortfoliosController as UserPortfoliosController,
    Certification\CertificationsController as UserCertificationsController,
    Profile\ProfileController as UserProfileController,
    CV\CVController,
    Profile\TwoFactorRememberTokenController as UserTwoFactorRememberTokenController,
    Technology\TechnologiesController as TechnologyController,
    Video\VideosController
};
use App\Http\Controllers\Auth\{AuthenticatedSessionController,
    ConfirmablePasswordController,
    NewPasswordController,
    PasswordController,
    PasswordResetLinkController,
    TwoFactorController
};
use App\Http\Controllers\Admin\{CV\CVController as CVAdminController,
    DashboardController,
    Portfolio\PortfolioReportController,
    Portfolio\PortfoliosController,
    Certification\CertificationsController as AdminCertificationsController,
    Profile\ProfileController,
    Profile\TwoFactorRememberTokenController,
    ProjectTimeManagement\ProjectTimeManagementsController,
    Slider\SlidersController,
    Technology\TechnologyTypesController,
    User\UsersController,
    Upwork\UpworkIdsController,
    Technology\TechnologiesController,
    Communication\ToolsController,
    Businessanalyst\BusinessAnalystsController,
    Developer\DevelopersController,
    ProjectManager\ProjectManagersController,
    Project\ProjectController,
    Video\VideosController as VideosAdminController};
use Illuminate\Support\Facades\Route;

// Public routes
Route::get('/', function () {
    return view('welcome');
})->name('home');


// Admin Routes
Route::group(['prefix' => 'admin', 'as' => 'admin.', 'middleware' => ['admin', '2fa']], function () {
    // Dashboard route
    Route::get('dashboard', [DashboardController::class, 'index'])->name('dashboard');
    Route::get('dashboard-technologies-count', [DashboardController::class, 'getTechnologiesCountData'])->name('get-technologies-count-data');

    // Security route
    Route::get('update-password', [ProfileController::class, 'security'])->name('profile.security')
        ->middleware('password.confirm');
    Route::post('password-update', [ProfileController::class, 'updatePassword'])->name('profile.password.update');

    // Project route

    // Profile routes
    Route::get('profile', [ProfileController::class, 'profile'])->name('profile.index');
    Route::post('profile-update', [ProfileController::class, 'updateProfile'])->name('profile.update');

    // Remember device routes
    Route::get('remember-devices', [TwoFactorRememberTokenController::class, 'index'])
        ->name('remember-devices.index');
    Route::get('remember-devices/search', [TwoFactorRememberTokenController::class, 'search'])->name('remember-devices.search');
    Route::delete('remember-devices/{rememberToken}', [TwoFactorRememberTokenController::class, 'destroy'])
        ->name('remember-devices.destroy');

    // Two factor routes
    Route::post('two-factor-update', [ProfileController::class, 'updateTwoFactor'])->name('profile.two-factor.update');

    // User routes
    Route::get('users/trash', [UsersController::class, 'trash'])->name('users.trash');
    Route::post('users/{user}/restore', [UsersController::class, 'restore'])->name('users.restore');
    Route::resource('users', UsersController::class);

    // UpworkId routes
    Route::resource('upwork-ids', UpworkIdsController::class);

    // Technology type routes
    Route::resource('technology-types', TechnologyTypesController::class);

    // Technologies routes
    Route::resource('technologies', TechnologiesController::class);

    // Communication Tool routes
    Route::resource('communication-tools', ToolsController::class);

    // Business Analyst routes
    Route::resource('business-analysts', BusinessAnalystsController::class);

    // Project Manager routes
    Route::resource('project-managers', ProjectManagersController::class);

    // Developer routes
    Route::resource('resource-names', DevelopersController::class);

    // Portfolio routes
    Route::post('portfolios/file-upload', [PortfoliosController::class, 'fileUpload'])
        ->name('portfolios.file-upload.store');

    Route::post('portfolios/{portfolio}/archive-status-change', [PortfoliosController::class, 'archiveStatusChange'])
        ->name('portfolios.archive-status-change');
    Route::get('portfolios/archived', [PortfoliosController::class, 'archived'])
        ->name('portfolios.archived');

    Route::get('short-portfolio/create', [PortfoliosController::class, 'shortPortfolioCreate'])->name('shortPortfolio.create');
    Route::post('short-portfolio/store', [PortfoliosController::class, 'shortPortfolioStore'])->name('shortPortfolio.store');
    Route::get('short-portfolio/{portfolio}/edit', [PortfoliosController::class, 'shortPortfolioEdit'])->name('shortPortfolio.edit');
    Route::put('short-portfolio/{portfolio}/update', [PortfoliosController::class, 'shortPortfolioUpdate'])->name('shortPortfolio.update');
    Route::get('short-portfolio/{portfolio}/show', [PortfoliosController::class, 'shortPortfolioShow'])->name('shortPortfolio.show');
    Route::post('short-portfolio/{portfolio}/archive-status-change', [PortfoliosController::class, 'archiveStatusChange'])->name('shortPortfolio.archive-status-change');
    Route::get('short-portfolio/archived', [PortfoliosController::class, 'shortPortfolioArchived'])->name('shortPortfolio.archived');
    Route::get('portfolios/search', [PortfoliosController::class, 'portfolioSearch'])->name('portfolios.search');
    Route::get('portfolios/ajax-search', [PortfoliosController::class, 'ajaxPortfolioSearch'])->name('portfolios.ajax-search');
    Route::resource('portfolios', PortfoliosController::class);

    // Portfolio report routes
    Route::get('portfolio-reports', [PortfolioReportController::class, 'index'])->name('portfolio-reports.index');

    // Certification routes
    Route::resource('certifications', AdminCertificationsController::class);

    // Project Time Management routes
    Route::resource('project-time-managements', ProjectTimeManagementsController::class);

    // Video routes
    Route::post('videos/file-upload', [VideosAdminController::class, 'fileUpload'])
        ->name('videos.file-upload.store');
    Route::resource('videos', VideosAdminController::class);

    // CV routes
    Route::post('cv/file-upload', [CVAdminController::class, 'fileUpload'])
        ->name('cv.file-upload.store');
    Route::resource('cv', CVAdminController::class);

    // Sliders route
    Route::post('sliders/update-images', [SlidersController::class, 'updateImages'])->name('sliders.update-images');
    Route::resource('sliders', SlidersController::class);
});

#-------------------------------------------------------------------------------------------------------------------

// User Routes
Route::group(['middleware' => ['auth', '2fa']], function () {
    // Dashboard route
    Route::get('dashboard', UserDashboardController::class)->name('dashboard');
    Route::get('dashboard-technologies-count', [UserDashboardController::class, 'getTechnologiesCountData'])->name('get-technologies-count-data');

    // Security route
    Route::get('update-password', [UserProfileController::class, 'security'])->name('profile.security')
        ->middleware('password.confirm');
    Route::post('password-update', [UserProfileController::class, 'updatePassword'])->name('profile.password.update');

    // Profile routes
    Route::get('profile', [UserProfileController::class, 'profile'])->name('profile.index');
    Route::post('profile-update', [UserProfileController::class, 'updateProfile'])->name('profile.update');

    // Remember device routes
    Route::get('remember-devices', [UserTwoFactorRememberTokenController::class, 'index'])
        ->name('remember-devices.index');
    Route::get('remember-devices/search', [UserTwoFactorRememberTokenController::class, 'search'])->name('remember-devices.search');
    Route::delete('remember-devices/{rememberToken}', [UserTwoFactorRememberTokenController::class, 'destroy'])
        ->name('remember-devices.destroy');

    // Two factor routes
    Route::post('two-factor-update', [UserProfileController::class, 'updateTwoFactor'])
        ->name('profile.two-factor.update');

    // Portfolio routes
    Route::post('portfolios/file-upload', [UserPortfoliosController::class, 'fileUpload'])
        ->name('portfolios.file-upload.store');
    Route::get('short-portfolio/create', [UserPortfoliosController::class, 'shortPortfolioCreate'])->name('shortPortfolio.create');
    Route::post('short-portfolio/store', [UserPortfoliosController::class, 'shortPortfolioStore'])->name('shortPortfolio.store');
    Route::get('short-portfolio/{portfolio}/edit', [UserPortfoliosController::class, 'shortPortfolioEdit'])->name('shortPortfolio.edit');
    Route::put('short-portfolio/{portfolio}/update', [UserPortfoliosController::class, 'shortPortfolioUpdate'])->name('shortPortfolio.update');
    Route::get('short-portfolio/{portfolio}/show', [UserPortfoliosController::class, 'shortPortfolioShow'])->name('shortPortfolio.show');
    Route::get('portfolios/search', [UserPortfoliosController::class, 'portfolioSearch'])->name('portfolios.search');
    Route::get('portfolios/ajax-search', [UserPortfoliosController::class, 'ajaxPortfolioSearch'])->name('portfolios.ajax-search');
    Route::resource('portfolios', UserPortfoliosController::class);

    // Certification routes
    Route::resource('certifications', UserCertificationsController::class);

    // Video routes
    Route::post('videos/file-upload', [VideosController::class, 'fileUpload'])
        ->name('videos.file-upload.store');
    Route::resource('videos', VideosController::class);

    // CV routes
    Route::post('cv/file-upload', [CVController::class, 'fileUpload'])
        ->name('cv.file-upload.store');
    Route::resource('cv', CVController::class);

    // Communication Tool routes
    Route::resource('communication-tools', UserToolsController::class);

    // Technologies routes
    Route::get('technologies/{technology}', [TechnologyController::class, 'show'])->name('technologies.show');
    Route::get('technologies/{technology}/portfolios/search', [TechnologyController::class, 'portfolioSearch'])->name('technologies.portfolios.search');
});
#-------------------------------------------------------------------------------------------------------------------

// User auth routes
Route::middleware('guest')->group(function () {
    Route::get('login', [AuthenticatedSessionController::class, 'create'])
        ->name('login');

    Route::post('login', [AuthenticatedSessionController::class, 'store']);

    Route::get('forgot-password', [PasswordResetLinkController::class, 'create'])
        ->name('password.request');

    Route::post('forgot-password', [PasswordResetLinkController::class, 'store'])
        ->name('password.email');

    Route::get('reset-password/{token}', [NewPasswordController::class, 'create'])
        ->name('password.reset');

    Route::post('reset-password', [NewPasswordController::class, 'store'])
        ->name('password.store');
});

// Auth routes
Route::group([], function () {
    // Two factor routes
    Route::get('2fa/verify', [TwoFactorController::class, 'showVerifyForm'])->name('2fa.verify');
    Route::post('2fa/verify', [TwoFactorController::class, 'verify']);
    Route::post('2fa/resend', [TwoFactorController::class, 'resendTwoFactorCode'])->name('2fa.resend');
    Route::get('2fa/mode', [TwoFactorController::class, 'showVerifyMode'])->name('2fa.mode');
    Route::post('2fa/mode', [TwoFactorController::class, 'twoFactorMode']);
    Route::get('confirm-password', [ConfirmablePasswordController::class, 'show'])
        ->name('password.confirm');

    Route::post('confirm-password', [ConfirmablePasswordController::class, 'store']);

    Route::put('password', [PasswordController::class, 'update'])->name('password.update');

    Route::post('logout', [AuthenticatedSessionController::class, 'destroy'])
        ->name('logout');
});
// End of User Auth Routes
