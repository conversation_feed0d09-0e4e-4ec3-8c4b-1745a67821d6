<?php if (isset($component)) { $__componentOriginalf56340035eeb7de26edb3ee88689cd6b = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf56340035eeb7de26edb3ee88689cd6b = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.user.app','data' => ['title' => $page_title,'fromUrl' => route('portfolios.index')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('user.app'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($page_title),'from_url' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('portfolios.index'))]); ?>

     <?php $__env->slot('breadcrumbSlot', null, []); ?> 
        <a href="<?php echo e(route('portfolios.index')); ?>" class="btn btn-dark">
            <i class="fa fa-arrow-left"></i>
            <span>Back</span>
        </a>
     <?php $__env->endSlot(); ?>

    <div class="row justify-content-center mb-5">
        <div class="col-md-6">
            <div class="card">

                <div class="card-body">
                    <?php if (isset($component)) { $__componentOriginalf9a5f060e1fbbcbc7beb643b113b10ab = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf9a5f060e1fbbcbc7beb643b113b10ab = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form','data' => ['action' => ''.e(route('portfolios.store')).'','id' => 'portfolioForm']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['action' => ''.e(route('portfolios.store')).'','id' => 'portfolioForm']); ?>
                        <div class="form-group mb-3">
                            <label for="project-name" class="ms-2">
                                Upwork Project Name
                                <span class="text-danger">*</span>
                            </label>
                            <input type="text" id="project-name"
                                   class="form-control <?php $__errorArgs = ['project_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                   name="project_name" autocomplete="project-name"
                                   placeholder="Enter upwork project name"
                                   required value="<?php echo e(old('project_name')); ?>" autofocus>
                            <?php $__errorArgs = ['project_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback">
                                <?php echo e($message); ?>

                            </div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="form-group mb-3">
                            <label for="job-url" class="ms-2">
                                Upwork Job URL
                            </label>
                            <input type="url" id="job-url"
                                   class="form-control <?php $__errorArgs = ['job_url'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                   name="job_url"
                                   placeholder="Enter upwork job url"
                                   value="<?php echo e(old('job_url')); ?>">
                            <?php $__errorArgs = ['job_url'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback">
                                <?php echo e($message); ?>

                            </div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="form-group mb-3">
                            <label for="client-name" class="ms-2">
                                Client Name
                                <span class="text-danger">*</span>
                            </label>
                            <input type="text" id="client-name"
                                   class="form-control <?php $__errorArgs = ['client_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" name="client_name"
                                   placeholder="Enter client name" autocomplete="client-name"
                                   required value="<?php echo e(old('client_name')); ?>">
                            <?php $__errorArgs = ['client_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback">
                                <?php echo e($message); ?>

                            </div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="form-group mb-3">
                            <label for="client-email" class="ms-2">
                                Client Email
                            </label>
                            <input type="text" id="client-email"
                                   class="form-control <?php $__errorArgs = ['client_email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" name="client_email"
                                   placeholder="Enter client email" autocomplete="email"
                                   value="<?php echo e(old('client_email')); ?>">
                            <?php $__errorArgs = ['client_email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback">
                                <?php echo e($message); ?>

                            </div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="form-group mb-3" id="client-phone-section">
                            <label for="client-phone" class="ms-2">
                                Client Phone
                            </label>
                            <input type="text" id="client-phone" name="client_phone"
                                   class="form-control <?php $__errorArgs = ['client_phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                   value="<?php echo e(old('client_phone')); ?>">

                            <div class="invalid-feedback" style="display: none;" id="client-phone-error-msg">

                            </div>
                            <?php $__errorArgs = ['client_phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback">
                                <?php echo e($message); ?>

                            </div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>

                            <input type="hidden" name="client_phone_country_code"
                                   value="<?php echo e(old('client_phone_country_code')); ?>"
                                   id="client-phone-country-code">
                        </div>

                        <div class="form-group" id="client-type-section">
                            <label for="client-type">
                                Client Type
                                <span class="text-danger">*</span>
                            </label>
                            <select name="client_type" id="client-type" required
                                    class="form-control <?php $__errorArgs = ['client_type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                                <option value="">Select Client Type</option>
                                <?php $__currentLoopData = $clientTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $clientType): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($key); ?>"
                                            <?php if(old('client_type') == $key): ?> selected <?php endif; ?>>
                                        <?php echo e($clientType); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <?php $__errorArgs = ['client_type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback">
                                <?php echo e($message); ?>

                            </div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="form-group mb-3" id="client-name-ref-section">
                            <label for="client-name-ref" class="ms-2">
                                Client Reference Name
                                <span class="text-danger">*</span>
                            </label>
                            <input type="text" id="client-name-ref"
                                   class="form-control <?php $__errorArgs = ['client_name_ref'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                   name="client_name_ref"
                                   placeholder="Enter client reference name"
                                   value="<?php echo e(old('client_name_ref')); ?>">
                            <?php $__errorArgs = ['client_name_ref'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback">
                                <?php echo e($message); ?>

                            </div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="form-group" id="client-ref-type-section">
                            <label for="client-ref-type">
                                Client Reference Type
                                <span class="text-danger">*</span>
                            </label>
                            <select name="client_ref_type" id="client-ref-type"
                                    class="form-control <?php $__errorArgs = ['client_ref_type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                                <option value="">Select Client Reference Type</option>
                                <?php $__currentLoopData = $clientRefTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $clientRefType): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($key); ?>"
                                            <?php if(old('client_ref_type') == $key): ?> selected <?php endif; ?>>
                                        <?php echo e($clientRefType); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>

                            <?php $__errorArgs = ['client_ref_type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback">
                                <?php echo e($message); ?>

                            </div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="form-group" id="contract-type-section">
                            <label for="contract-type">
                                Contract Type
                                <span class="text-danger">*</span>
                            </label>
                            <select name="contract_type" id="contract-type"
                                    class="form-control <?php $__errorArgs = ['contract_type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                                <option value="">Select Client Reference Type</option>
                                <?php $__currentLoopData = $contractTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $contractType): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($key); ?>"
                                            <?php if(old('contract_type') == $key): ?> selected <?php endif; ?>>
                                        <?php echo e($contractType); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>

                            <?php $__errorArgs = ['contract_type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback">
                                <?php echo e($message); ?>

                            </div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="form-group mb-3" id="contract-type-rate-section">
                            <label for="contract-type-rate" class="ms-2">
                                Contract Type Rate
                                <span class="text-danger">*</span>
                            </label>
                            <input type="number" min="1" id="contract-type-rate"
                                   class="form-control <?php $__errorArgs = ['contract_type_rate'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                   name="contract_type_rate"
                                   placeholder="Enter contract type rate"
                                   value="<?php echo e(old('contract_type_rate')); ?>">
                            <?php $__errorArgs = ['contract_type_rate'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback">
                                <?php echo e($message); ?>

                            </div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="form-group">
                            <label for="upwork-id">
                                Upwork ID
                                <span class="text-danger">*</span>
                            </label>
                            <select name="upwork_id" id="upwork-id" required
                                    class="form-control <?php $__errorArgs = ['upwork_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                                <option value="">Select Upwork ID</option>
                                <?php $__currentLoopData = $upworkIds; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $upworkID): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($upworkID->id); ?>"
                                            <?php if(old('upwork_id') == $upworkID->id): ?> selected <?php endif; ?>>
                                        <?php echo e($upworkID->upwork_id); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <?php $__errorArgs = ['upwork_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback">
                                <?php echo e($message); ?>

                            </div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>

                            <input type="text" id="upwork-id-other" name="upwork_id_other"
                                   value="<?php echo e(old('upwork_id_other')); ?>"
                                   class="form-control mt-2 <?php $__errorArgs = ['upwork_id_other'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                   placeholder="Enter other upwork ID"
                                   <?php echo e(old('upwork_id_text') == 'other' ? 'required' : ''); ?>

                                   style="<?php echo e(old('upwork_id_text') == 'other' ? 'display: block;' : 'display: none;'); ?>">
                            <?php $__errorArgs = ['upwork_id_other'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback">
                                <?php echo e($message); ?>

                            </div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>

                            <input type="hidden" id="upwork-id-other-text" name="upwork_id_text"
                                   value="<?php echo e(old('upwork_id_text')); ?>">
                        </div>

                        <div class="form-group">
                            <label for="developer">
                                Resource Names
                                <span class="text-danger">*</span>
                            </label>
                            <select name="developers[]" id="developer" multiple
                                    class="form-control <?php $__errorArgs = ['developers'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                                <option value="">Select Or Create RNs</option>
                                <?php $__currentLoopData = $developers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $developer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($developer->id); ?>"
                                            <?php if(in_array($developer->id, old('developers', []))): ?> selected <?php endif; ?>>
                                        <?php echo e($developer->developer); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <?php $__errorArgs = ['developers'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback">
                                <?php echo e($message); ?>

                            </div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="form-group">
                            <label for="project-manager">
                                Project Managers
                                <span class="text-danger">*</span>
                            </label>
                            <select name="project_managers[]" id="project-manager" multiple
                                    class="form-control <?php $__errorArgs = ['project_managers'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                                <option value="">Select Or Create PM's</option>
                                <?php $__currentLoopData = $projectManagers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $projectManager): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($projectManager->id); ?>"
                                            <?php if(in_array($projectManager->id, old('project_managers', []))): ?> selected <?php endif; ?>>
                                        <?php echo e($projectManager->project_manager); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <?php $__errorArgs = ['project_managers'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback">
                                <?php echo e($message); ?>

                            </div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="form-group">
                            <label for="business-analyst">
                                Business Analysts
                                <span class="text-danger">*</span>
                            </label>
                            <select name="business_analysts[]" id="business-analyst" multiple
                                    class="form-control <?php $__errorArgs = ['business_analysts'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                                <option value="">Select Or Create BA's</option>
                                <?php $__currentLoopData = $businessAnalysts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $businessAnalyst): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($businessAnalyst->id); ?>"
                                            <?php if(in_array($businessAnalyst->id, old('business_analysts', []))): ?> selected <?php endif; ?>>
                                        <?php echo e($businessAnalyst->business_analyst); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <?php $__errorArgs = ['business_analysts'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback">
                                <?php echo e($message); ?>

                            </div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="form-group">
                            <label for="technology">
                                Technologies
                                <span class="text-danger">*</span>
                            </label>
                            <select name="technologies[]" id="technology" multiple
                                    class="form-control <?php $__errorArgs = ['technologies'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                                <option value="">Select Or Create Techs</option>
                                <?php $__currentLoopData = $technologies; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $technology): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($technology->id); ?>"
                                            <?php if(in_array($technology->id, old('technologies', []))): ?> selected <?php endif; ?>>
                                        <?php echo e($technology->technology); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <?php $__errorArgs = ['technologies'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback">
                                <?php echo e($message); ?>

                            </div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="form-group mb-3">
                            <label for="site-url">
                                Site URL
                            </label>
                            <input type="url" name="site_url"
                                   class="form-control <?php $__errorArgs = ['site_url'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="site-url"
                                   placeholder="Enter site url" value="<?php echo e(old('site_url')); ?>">
                            <?php $__errorArgs = ['site_url'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback">
                                <?php echo e($message); ?>

                            </div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="form-group mb-3">
                            <label for="project-description">
                                Project Description (Please write credentials on top for the project)
                                <span class="text-danger">*</span>
                            </label>
                            <textarea name="project_description" id="project-description" cols="30"
                                      rows="10" placeholder="Enter project description"
                                      class="form-control <?php $__errorArgs = ['project_description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"><?php echo e(old('project_description')); ?></textarea>
                            <?php $__errorArgs = ['project_description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback">
                                <?php echo e($message); ?>

                            </div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="form-group mb-3">
                            <label for="start-date" class="ms-2">
                                Start Date
                            </label>
                            <input type="date" id="start-date"
                                   class="form-control <?php $__errorArgs = ['start_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" name="start_date"
                                   placeholder="Select start date"
                                   value="<?php echo e(old('start_date')); ?>">
                            <?php $__errorArgs = ['start_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback">
                                <?php echo e($message); ?>

                            </div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="form-group" id="communication-tools-section">
                            <label for="communication-tool">
                                Communication Tools
                            </label>
                            <select name="communication_tools[]" id="communication-tool" multiple
                                    class="form-control <?php $__errorArgs = ['communication_tools'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                                <option value="">Select Tools</option>
                                <?php $__currentLoopData = $communicationTools; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $communicationTool): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($communicationTool->id); ?>"
                                            data-id="<?php echo e($communicationTool->id); ?>"
                                            data-name="<?php echo e($communicationTool->communication_tool); ?>"
                                            data-required-fields="<?php echo e(json_encode($communicationTool->required_fields)); ?>"
                                            <?php if(in_array($communicationTool->id, old('communication_tools', []))): ?>
                                                selected <?php endif; ?>>
                                        <?php echo e($communicationTool->communication_tool); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <?php $__errorArgs = ['communication_tools'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback">
                                <?php echo e($message); ?>

                            </div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div id="communication-tool-items">
                        </div>

                        <div id="attachment-files">

                        </div>
                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf9a5f060e1fbbcbc7beb643b113b10ab)): ?>
<?php $attributes = $__attributesOriginalf9a5f060e1fbbcbc7beb643b113b10ab; ?>
<?php unset($__attributesOriginalf9a5f060e1fbbcbc7beb643b113b10ab); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf9a5f060e1fbbcbc7beb643b113b10ab)): ?>
<?php $component = $__componentOriginalf9a5f060e1fbbcbc7beb643b113b10ab; ?>
<?php unset($__componentOriginalf9a5f060e1fbbcbc7beb643b113b10ab); ?>
<?php endif; ?>

                    <div class="form-group">
                        <label for="attachments">Attachments</label>
                        <?php if (isset($component)) { $__componentOriginalf9a5f060e1fbbcbc7beb643b113b10ab = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf9a5f060e1fbbcbc7beb643b113b10ab = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form','data' => ['action' => ''.e(route('portfolios.file-upload.store')).'','class' => 'dropzone','id' => 'dropzoneForm','enctype' => 'multipart/form-data']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['action' => ''.e(route('portfolios.file-upload.store')).'','class' => 'dropzone','id' => 'dropzoneForm','enctype' => 'multipart/form-data']); ?>
                            <div class="fallback">
                                <input name="file" type="file" multiple class="form-control" id="attachments"/>
                            </div>
                         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf9a5f060e1fbbcbc7beb643b113b10ab)): ?>
<?php $attributes = $__attributesOriginalf9a5f060e1fbbcbc7beb643b113b10ab; ?>
<?php unset($__attributesOriginalf9a5f060e1fbbcbc7beb643b113b10ab); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf9a5f060e1fbbcbc7beb643b113b10ab)): ?>
<?php $component = $__componentOriginalf9a5f060e1fbbcbc7beb643b113b10ab; ?>
<?php unset($__componentOriginalf9a5f060e1fbbcbc7beb643b113b10ab); ?>
<?php endif; ?>
                    </div>

                </div>

                <div class="card-footer">
                    <p class="text-right">
                        <a href="<?php echo e(route('portfolios.index')); ?>" class="btn btn-danger mr-2">
                            <i class="fa fa-window-close"></i>
                            Cancel
                        </a>

                        <button type="submit" class="btn btn-primary" id="submitBtn">
                            <i class="fa fa-save"></i>
                            Save
                        </button>
                    </p>
                </div>


            </div>
        </div>
    </div>

     <?php $__env->slot('bottomStyles', null, []); ?> 
        <style>
            .select2-selection__rendered {
                line-height: 32px !important;
            }

            .select2-container .select2-selection--single {
                height: 36px !important;
            }

            .select2-selection__arrow {
                height: 35px !important;
            }
        </style>
     <?php $__env->endSlot(); ?>

     <?php $__env->slot('styles', null, []); ?> 
        <link rel="stylesheet" href="<?php echo e(asset('assets/admin/css/plugins/select2/select2.min.css')); ?>">
        <!-- DROPZONE -->
        <link href="<?php echo e(asset('assets/admin/css/plugins/dropzone/dropzone.css')); ?>" rel="stylesheet">
        <!-- MAILTIP -->
        <link rel="stylesheet" href="<?php echo e(asset('assets/admin/js/plugins/mailtip-autosuggestions/css/mailtip.css')); ?>">

        <link rel="stylesheet" href="<?php echo e(asset('assets/admin/js/plugins/intl-tel-input/css/intlTelInput.min.css')); ?>">
     <?php $__env->endSlot(); ?>

     <?php $__env->slot('scripts', null, []); ?> 
        <script src="<?php echo e(asset('assets/admin/js/plugins/select2/select2.full.min.js')); ?>"></script>
        <!-- DROPZONE -->
        <script src="<?php echo e(asset('assets/admin/js/plugins/dropzone-for-portfolio/dropzone.js')); ?>"></script>
        <!-- MAILTIP -->
        <script src="<?php echo e(asset('assets/admin/js/plugins/mailtip-autosuggestions/js/jquery.mailtip.js')); ?>"></script>

        <script src="<?php echo e(asset('assets/admin/js/plugins/intl-tel-input/js/intlTelInput.min.js')); ?>"></script>
        <script src="<?php echo e(asset('assets/admin/js/plugins/intl-tel-input/js/intlTelInput-jquery.min.js')); ?>"></script>
     <?php $__env->endSlot(); ?>

     <?php $__env->slot('bottomScripts', null, []); ?> 
        <script type="text/javascript">
            $(document).ready(function () {
                $('#upwork-id').select2({
                    placeholder: 'Select Upwork ID',
                    allowClear : true
                });

                $('#developer').select2({
                    placeholder: 'Select Or Create RNs',
                    multiple   : true,
                    allowClear : true,
                    tags       : true
                });
                $('#project-manager').select2({
                    placeholder: "Select Or Create PM's",
                    multiple   : true,
                    allowClear : true,
                    tags       : true
                });

                $('#business-analyst').select2({
                    placeholder: "Select Or Create BA's",
                    multiple   : true,
                    allowClear : true,
                    tags       : true
                });

                $('#technology').select2({
                    placeholder: 'Select Or Create Techs',
                    multiple   : true,
                    allowClear : true,
                    tags       : true
                });

                $('#communication-tool').select2({
                    placeholder: 'Select Tools',
                    multiple   : true,
                    allowClear : true
                });

                $('#client-type').select2({
                    placeholder: 'Select Client Type',
                    allowClear : true
                });

                $('#submitBtn').on('click', function () {
                    $('#portfolioForm').submit();
                });

                $('#upwork-id').on('change', function () {
                    // selected value
                    let selectedValue = $('#upwork-id').find('option:selected').text();
                    selectedValue = selectedValue.trim().toLowerCase();
                    if (selectedValue === 'other') {
                        $('#upwork-id-other').show();
                        $('#upwork-id-other').attr('required', true);
                        $('#upwork-id-other-text').val('other');
                    } else {
                        $('#upwork-id-other').hide();
                        $('#upwork-id-other').removeAttr('required');
                        $('#upwork-id-other-text').val('');
                    }
                });

                var oldSelectedTools = <?php echo json_encode(old('data', [])); ?>;
                var oldCommunicationTools = <?php echo json_encode(old('communication_tools', [])); ?>;

                function communicationToolsInit() {
                    let selectedOptions = $('#communication-tool').val();
                    console.log(selectedOptions)
                    const communicationToolItems = $('#communication-tool-items');
                    let html = '';
                    // for each selected option and select option data
                    if (selectedOptions.length) {
                        selectedOptions.forEach(function (selectedOption) {
                            let selectedOptionData = $('#communication-tool').find('option[value="' + selectedOption + '"]');
                            let selectedOptionName = selectedOptionData.data('name');
                            console.log('selectedOptionName', selectedOptionName)
                            let requiredFields = selectedOptionData.data('required-fields');
                            // if selected option has required fields
                            if (requiredFields && requiredFields.length) {
                                html = `<div class="card mb-3" id="communication-tool-item-${selectedOption}">
                                <div class="card-body">
                                    <h4 class="card-title">
                                        ${selectedOptionName} Required Fields
                                    </h4>`;
                                // for each required field
                                requiredFields.forEach(function (requiredField) {
                                    // if required field is not already added
                                    if (!$(`#${requiredField + '-' + selectedOption}`).length) {
                                        let oldVal = '';
                                        if (oldSelectedTools[selectedOption]) {
                                            oldVal = oldSelectedTools[selectedOption][requiredField] ?? '';
                                            if (oldVal === 'null') {
                                                oldVal = '';
                                            }
                                        }
                                        let formatSelectedOptionName = selectedOptionName.trim().toLowerCase();
                                        // add required field
                                        if (formatSelectedOptionName === 'upwork') {
                                            html += `<div class="form-group" id="${requiredField}">
                                            <label for="${requiredField}-${selectedOption}">${requiredField.replace('_', ' ').toUpperCase()} ( By Chat, By Calling, Both )</label>
                                            <select name="data[${selectedOption}][${requiredField}]" id="${requiredField}-${selectedOption}" class="form-control">
                                                <option value="by_chat" ${oldVal === 'by_chat' ? 'selected' : ''}>By Chat</option>
                                                <option value="by_calling" ${oldVal === 'by_calling' ? 'selected' : ''}>By Calling</option>
                                                <option value="both" ${oldVal === 'both' ? 'selected' : ''}>Both</option>
                                            </select>
                                        </div>`;
                                        } else {
                                            html += `<div class="form-group" id="${requiredField}">
                                            <label for="${requiredField}-${selectedOption}">${requiredField.replace('_', ' ').toUpperCase()}</label>
                                            <input type="text" name="data[${selectedOption}][${requiredField}]" id="${requiredField}-${selectedOption}"
                                                   class="form-control" placeholder="Enter ${requiredField.replace('_', ' ')}" value="${oldVal}">
                                            </div>`;
                                        }
                                    }
                                });
                                html += `</div></div>`;
                            }

                            if (!$(`#communication-tool-item-${selectedOption}`).length) {
                                communicationToolItems.append(html);
                            }

                            // remove unselected options
                            $('#communication-tool-items').find('.card').each(function () {
                                let cardId = $(this).attr('id');
                                let cardIdArray = cardId.split('-');
                                let cardIdValue = cardIdArray[cardIdArray.length - 1];
                                if (!selectedOptions.includes(cardIdValue)) {
                                    $(this).remove();
                                }
                            });
                        });
                    } else {
                        communicationToolItems.html('');
                    }
                }

                $('#communication-tool').on('change', function () {
                    // selected option
                    communicationToolsInit();
                });

                if (oldSelectedTools.length || oldCommunicationTools.length) {
                    communicationToolsInit();
                }

                function clientTypeFieldsInit() {
                    // selected value
                    let selectedValue = $('#client-type').find('option:selected').val();
                    selectedValue = selectedValue.trim().toLowerCase();

                    // sections
                    const clientNameRefSection = $('#client-name-ref-section');
                    const clientRefTypeSection = $('#client-ref-type-section');
                    const contractTypeSection = $('#contract-type-section');
                    const contractTypeRateSection = $('#contract-type-rate-section');

                    // fields
                    const clientNameRef = $('#client-name-ref');
                    const clientRefType = $('#client-ref-type');
                    const contractType = $('#contract-type');
                    const contractTypeRate = $('#contract-type-rate');

                    if (selectedValue === 'client_ref') {
                        clientNameRefSection.show();
                        clientNameRef.attr('name', 'client_name_ref');
                        clientNameRef.attr('required', true);

                        clientRefTypeSection.show();
                        clientRefType.attr('name', 'client_ref_type');
                        clientRefType.attr('required', true);

                        contractTypeSection.show();
                        contractType.attr('name', 'contract_type');
                        contractType.attr('required', true);

                        contractTypeRateSection.show();
                        contractTypeRate.attr('name', 'contract_type_rate');
                        contractTypeRate.attr('required', true);

                        initClientTypeSelectBox();
                    } else {
                        clientNameRefSection.hide();
                        clientNameRef.removeAttr('name');
                        clientNameRef.removeAttr('required');
                        clientNameRef.val('');

                        clientRefTypeSection.hide();
                        clientRefType.removeAttr('name');
                        clientRefType.removeAttr('required');
                        clientRefType.val('');

                        contractTypeSection.hide();
                        contractType.removeAttr('name');
                        contractType.removeAttr('required');
                        contractType.val('');

                        contractTypeRateSection.hide();
                        contractTypeRate.removeAttr('name');
                        contractTypeRate.removeAttr('required');
                        contractTypeRate.val('');
                    }
                }

                function initClientTypeSelectBox() {
                    $('#client-ref-type').select2({
                        placeholder: 'Select Client Reference Type',
                        allowClear : true
                    });

                    $('#contract-type').select2({
                        placeholder: 'Select Contract Type',
                        allowClear : true
                    });
                }

                $('#client-type').on('change', function () {
                    clientTypeFieldsInit();
                });

                clientTypeFieldsInit();
            });
        </script>

        <script type="text/javascript">
            $(document).ready(function () {
                // mailtip configurations
                $('#client-email').mailtip({
                    mails: <?php echo json_encode($mailPostfixes, 15, 512) ?>
                });

                const input = document.querySelector("#client-phone");
                const iti = window.intlTelInput(input, {
                    separateDialCode: true,
                    autoPlaceholder : "off",
                    utilsScript     : "<?php echo e(asset('assets/admin/js/plugins/intl-tel-input/js/utils.js')); ?>",
                });

                const oldCountryCode = "<?php echo e(old('client_phone_country_code')); ?>";
                const oldPhoneNumber = "<?php echo e(old('client_phone')); ?>";
                if (oldCountryCode && oldPhoneNumber) {
                    iti.setNumber('+' + '' + oldCountryCode + '' + oldPhoneNumber);
                }

                $('#client-phone').on('blur', function () {
                    console.log('iti.getNumber()', iti.getNumber())
                    console.log('value', $(this).val())
                    console.log('iti.isValidNumber()', iti.isValidNumber())
                    if ($('#client-phone').val()) {
                        const isValid = iti.isValidNumber();
                        if (!isValid) {
                            $('#client-phone-error-msg').html('Please enter valid phone number!');
                            $('#client-phone-error-msg').show();
                        } else {
                            $('#client-phone-error-msg').html('');
                            $('#client-phone-error-msg').hide();
                        }
                    } else {
                        $('#client-phone-country-code').val('');
                        $('#client-phone').val('');
                        $('#client-phone-error-msg').html('');
                        $('#client-phone-error-msg').hide();
                    }
                });

                $('#portfolioForm').on('submit', function () {
                    if ($('#client-phone').val()) {
                        const countryData = iti.getSelectedCountryData();
                        $('#client-phone-country-code').val(countryData.dialCode);
                        let number = iti.getNumber();
                        // remove country code from number
                        number = number.replace('+' + countryData.dialCode, '');
                        console.log('number', number)
                        $('#client-phone').val(number);

                        if (!iti.isValidNumber()) {
                            $('#client-phone').focus();
                            $('#client-phone-error-msg').html('Please enter valid phone number!');
                            $('#client-phone-error-msg').show();
                            return false;
                        } else {
                            $('#client-phone-error-msg').html('');
                            $('#client-phone-error-msg').hide();
                        }
                    } else {
                        $('#client-phone-error-msg').html('');
                        $('#client-phone-error-msg').hide();
                        $('#client-phone-country-code').val('');
                        $('#client-phone').val('');
                    }
                });
            });
        </script>
     <?php $__env->endSlot(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf56340035eeb7de26edb3ee88689cd6b)): ?>
<?php $attributes = $__attributesOriginalf56340035eeb7de26edb3ee88689cd6b; ?>
<?php unset($__attributesOriginalf56340035eeb7de26edb3ee88689cd6b); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf56340035eeb7de26edb3ee88689cd6b)): ?>
<?php $component = $__componentOriginalf56340035eeb7de26edb3ee88689cd6b; ?>
<?php unset($__componentOriginalf56340035eeb7de26edb3ee88689cd6b); ?>
<?php endif; ?>
<?php /**PATH D:\xampp8.2\htdocs\portfolio\resources\views/user/portfolios/create.blade.php ENDPATH**/ ?>