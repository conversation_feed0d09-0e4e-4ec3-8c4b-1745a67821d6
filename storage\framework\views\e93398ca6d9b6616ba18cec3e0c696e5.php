<?php if (isset($component)) { $__componentOriginalf56340035eeb7de26edb3ee88689cd6b = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf56340035eeb7de26edb3ee88689cd6b = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.user.app','data' => ['title' => $page_title]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('user.app'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($page_title)]); ?>

     <?php $__env->slot('breadcrumbSlot', null, []); ?> 
        <a href="<?php echo e(route('communication-tools.index')); ?>" class="btn btn-dark">
            <i class="fa fa-arrow-left"></i>
            <span>Back</span>
        </a>
     <?php $__env->endSlot(); ?>

    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card">
                <?php if (isset($component)) { $__componentOriginalf9a5f060e1fbbcbc7beb643b113b10ab = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf9a5f060e1fbbcbc7beb643b113b10ab = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form','data' => ['action' => ''.e(route('communication-tools.store')).'']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['action' => ''.e(route('communication-tools.store')).'']); ?>
                    <div class="card-body">
                        <div class="form-group mb-3">
                            <label for="communication-tool" class="ms-2">
                                Communication Tool
                                <span class="text-danger">*</span>
                            </label>
                            <input type="text" id="communication-tool"
                                   class="form-control <?php $__errorArgs = ['communication_tool'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                   name="communication_tool"
                                   placeholder="Enter communication tool"
                                   required value="<?php echo e(old('communication_tool')); ?>">
                            <?php $__errorArgs = ['communication_tool'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback">
                                <?php echo e($message); ?>

                            </div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="form-group mb-3">
                            <label for="required-fields" class="ms-2">
                                Required Fields
                                (Format Ex: username, email, workspace_url etc.)
                            </label>
                            <select name="required_fields[]" id="required-fields" multiple
                                    class="form-control <?php if($errors->has('required_fields') || $errors->has('required_fields.*')): ?> is-invalid <?php endif; ?>">
                            </select>
                            <?php $__errorArgs = ['required_fields'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback d-block">
                                <?php echo e($message); ?>

                            </div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            <?php $__errorArgs = ['required_fields.*'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback d-block">
                                <?php echo e($message); ?>

                            </div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                    </div>

                    <div class="card-footer">
                        <p class="text-right">
                            <a href="<?php echo e(route('communication-tools.index')); ?>" class="btn btn-danger mr-2">
                                <i class="fa fa-window-close"></i>
                                Cancel
                            </a>

                            <button type="submit" class="btn btn-primary">
                                <i class="fa fa-save"></i>
                                Save
                            </button>
                        </p>
                    </div>
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf9a5f060e1fbbcbc7beb643b113b10ab)): ?>
<?php $attributes = $__attributesOriginalf9a5f060e1fbbcbc7beb643b113b10ab; ?>
<?php unset($__attributesOriginalf9a5f060e1fbbcbc7beb643b113b10ab); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf9a5f060e1fbbcbc7beb643b113b10ab)): ?>
<?php $component = $__componentOriginalf9a5f060e1fbbcbc7beb643b113b10ab; ?>
<?php unset($__componentOriginalf9a5f060e1fbbcbc7beb643b113b10ab); ?>
<?php endif; ?>
            </div>
        </div>
    </div>

     <?php $__env->slot('styles', null, []); ?> 
        <link rel="stylesheet" href="<?php echo e(asset('assets/admin/css/plugins/select2/select2.min.css')); ?>">
     <?php $__env->endSlot(); ?>

     <?php $__env->slot('scripts', null, []); ?> 
        <script src="<?php echo e(asset('assets/admin/js/plugins/select2/select2.full.min.js')); ?>"></script>
     <?php $__env->endSlot(); ?>

     <?php $__env->slot('bottomScripts', null, []); ?> 
        <script type="text/javascript">
            $(document).ready(function () {
                $('#required-fields').select2({
                    placeholder: 'Enter required fields',
                    multiple   : true,
                    allowClear : true,
                    tags       : true
                });
            });
        </script>
     <?php $__env->endSlot(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf56340035eeb7de26edb3ee88689cd6b)): ?>
<?php $attributes = $__attributesOriginalf56340035eeb7de26edb3ee88689cd6b; ?>
<?php unset($__attributesOriginalf56340035eeb7de26edb3ee88689cd6b); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf56340035eeb7de26edb3ee88689cd6b)): ?>
<?php $component = $__componentOriginalf56340035eeb7de26edb3ee88689cd6b; ?>
<?php unset($__componentOriginalf56340035eeb7de26edb3ee88689cd6b); ?>
<?php endif; ?>
<?php /**PATH D:\xampp8.2\htdocs\portfolio\resources\views/user/communication/tool/create.blade.php ENDPATH**/ ?>