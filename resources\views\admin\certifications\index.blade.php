<x-admin.app :title="$page_title" :from_url="route('admin.dashboard')">

    <x-slot name="breadcrumbSlot">
        <a href="{{ route('admin.certifications.create') }}" class="btn btn-primary">
            <i class="fa fa-plus"></i>
            <span>Add New Certification</span>
        </a>
    </x-slot>

    <div class="top-header">
        <div class="top-header-heading">
            <div class="row justify-content-start">
                <div class="col">
                    <h2>Hello {{auth()->user()->first_name}} {{auth()->user()->last_name}} <img
                            src="{{ asset('assets/admin/img/waving.png')}}"></h2>
                </div>
            </div>
        </div>
        <div class="button-setup search-btn">
            <h4 class="card-title">
                <div class="row">
                    <div class="col">
                        <div class="form-group">
                            <input type="text" class="form-control" placeholder="Search certifications..." name="search" id="searchInput" value="{{ $request->get('search') }}">
                        </div>
                    </div>
                </div>
            </h4>
        </div>
    </div>

    <div class="row login-one">
        <div class="col-6 one">
            <div class="login-portfolio-add">
                <img class="custom-icon" src="{{ asset('assets/admin/img/port-new.png')}}">
            </div>
            <div class="login-portfolio-text">
                <span>Total Certifications</span>
                @if(isset($certificationCount))
                    <p>{{$certificationCount}}</p>
                @else
                    <p>0</p>
                @endif
            </div>
        </div>
        <div class="col-6 two">
            <div class="login-portfolio-add">
                <img class="custom-icon" src="{{ asset('assets/admin/img/port-today.png')}}">
            </div>
            <div class="login-portfolio-text">
                <span>Today's Certifications</span>
                @if(isset($certificationsAddedToday))
                    <p>{{$certificationsAddedToday}}</p>
                @else
                    <p>0</p>
                @endif
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <div class="row">
                <div class="col-md-6">
                    <h4 class="card-title">{{ $page_title }}</h4>
                </div>
                <div class="col-md-6 text-right">
                    <div class="btn-group">
                        <a href="{{ route('admin.certifications.create') }}" class="btn btn-primary">
                            <span>Add New Certification</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-md-4">
                    <select class="form-control" id="upworkIdFilter">
                        <option value="">All Upwork IDs</option>
                        @foreach($upworkIds as $upworkId)
                            <option value="{{ $upworkId->id }}" {{ $request->get('upwork_id') == $upworkId->id ? 'selected' : '' }}>
                                {{ $upworkId->upwork_id }}
                            </option>
                        @endforeach
                    </select>
                </div>
            </div>
            
            <div class="table-responsive" id="data-container">
                <table class="table table-bordered table-striped table-hover" id="certification-table">
                    <thead>
                    <tr>
                        <th>SL#</th>
                        <th>Certification Title</th>
                        <th>Upwork ID</th>
                        <th>User</th>
                        <th>Certification Date</th>
                        <th>File</th>
                        <th class="text-center">Action</th>
                    </tr>
                    </thead>
                    <tbody>
                    @forelse($certifications as $certification)
                        <tr class="editable">
                            <td>{{ $certifications->firstItem() + $loop->index }}</td>
                            <td>{{ $certification->certification_title ?? 'N/A' }}</td>
                            <td>{{ $certification->upworkID->upwork_id ?? 'N/A' }}</td>
                            <td>{{ $certification->owner->name ?? 'N/A' }}</td>
                            <td>{{ $certification->certification_date ? $certification->certification_date->format('M d, Y') : 'N/A' }}</td>
                            <td>
                                @if($certification->certification_upload)
                                    <a href="{{ Storage::url($certification->certification_upload) }}" target="_blank" class="btn btn-sm btn-info">
                                        <i class="fa fa-download"></i> View
                                    </a>
                                @else
                                    N/A
                                @endif
                            </td>
                            <td class="text-center">
                                <div class="btn-group">
                                    <a href="{{ route('admin.certifications.show', $certification->id) }}" class="btn btn-sm btn-info">
                                        <i class="fa fa-eye"></i>
                                    </a>
                                    <a href="{{ route('admin.certifications.edit', $certification->id) }}" class="btn btn-sm btn-warning">
                                        <i class="fa fa-edit"></i>
                                    </a>
                                    <form action="{{ route('admin.certifications.destroy', $certification->id) }}" method="POST" style="display: inline-block;">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this certification?')">
                                            <i class="fa fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="7" class="text-center">No certifications found.</td>
                        </tr>
                    @endforelse
                    </tbody>
                </table>
            </div>
            
            <div class="d-flex justify-content-center">
                {{ $certifications->links() }}
            </div>
        </div>
    </div>

    <x-slot:bottomScripts>
        <script type="text/javascript">
            $(document).ready(function () {
                // Search functionality
                $('#searchInput').on('keyup', function() {
                    let search = $(this).val();
                    let upworkId = $('#upworkIdFilter').val();
                    
                    $.ajax({
                        url: "{{ route('admin.certifications.index') }}",
                        type: 'GET',
                        data: {
                            search: search,
                            upwork_id: upworkId
                        },
                        success: function(response) {
                            $('#data-container').html(response);
                        }
                    });
                });

                // Filter functionality
                $('#upworkIdFilter').on('change', function() {
                    let upworkId = $(this).val();
                    let search = $('#searchInput').val();
                    
                    window.location.href = "{{ route('admin.certifications.index') }}?" + 
                        (upworkId ? 'upwork_id=' + upworkId : '') + 
                        (search ? '&search=' + search : '');
                });
            });
        </script>
    </x-slot>

</x-admin.app>
