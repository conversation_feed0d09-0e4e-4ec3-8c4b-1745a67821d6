<?php

namespace App\Http\Controllers\User\Certification;

use App\Http\Controllers\Controller;
use App\Http\Requests\User\Certification\CertificationRequest;
use App\Models\Certification;
use App\Models\UpworkId;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class CertificationsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        abort_unless(auth()->user()->role->name == 'user', 403, 'You are not authorized to view this page!');

        $page_title = 'Certifications';
        $search = $request->get('search');
        $page = $request->get('page', 1);
        $upworkId = $request->get('upwork_id');

        $certifications = Certification::query()
            ->where('user_id', auth()->id())
            ->latest()
            ->with(['upworkID']);

        if ($search) {
            $certifications->where(function ($query) use ($search) {
                $query->where('certification_title', 'like', '%' . $search . '%')
                    ->orWhereHas('upworkID', function ($query) use ($search) {
                        $query->where('upwork_id', 'like', '%' . $search . '%');
                    });
            });
        }

        if ($upworkId) {
            $certifications->where('upwork_id', $upworkId);
        }

        $certificationCount = Certification::where('user_id', auth()->id())->count();
        $certificationsAddedToday = Certification::where('user_id', auth()->id())
            ->whereDate('created_at', today())
            ->count();

        if ($certificationsAddedToday) {
            $finalCertification = $certificationCount - $certificationsAddedToday;
        } else {
            $finalCertification = $certificationCount;
        }

        $upworkIds = UpworkId::latest()->get();

        $certifications = $certifications->paginate(20, ['*'], 'page', $page);

        if ($request->ajax()) {
            $html = view('user.certifications.table_response', compact('certifications'))->render();
            return response()->json(['data' => $html]);
        }

        return view('user.certifications.index', compact(
            'page_title',
            'certifications',
            'request',
            'certificationsAddedToday',
            'finalCertification',
            'certificationCount',
            'upworkIds'
        ));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        abort_unless(auth()->user()->role->name == 'user', 403, 'You are not authorized to view this page!');

        $page_title = 'Create Certification';
        $upworkIds = UpworkId::latest()->get();

        return view('user.certifications.create', compact(
            'page_title',
            'upworkIds'
        ));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(CertificationRequest $request)
    {
        abort_unless(auth()->user()->role->name == 'user', 403, 'You are not authorized to view this page!');

        $data = $request->validated();

        // Handle file upload
        if ($request->hasFile('certification_upload')) {
            $file = $request->file('certification_upload');
            $fileName = time() . '_' . $file->getClientOriginalName();
            $filePath = $file->storeAs('certifications', $fileName, 'public');
            $data['certification_upload'] = $filePath;
        }

        $data['user_id'] = auth()->id();

        Certification::create($data);

        return redirect()->route('certifications.index')
            ->with('success', 'Certification created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Certification $certification)
    {
        abort_unless(auth()->user()->role->name == 'user', 403, 'You are not authorized to view this page!');
        abort_unless($certification->user_id == auth()->id(), 403, 'You are not authorized to view this certification!');

        $page_title = 'View Certification';
        $certification->load(['upworkID']);

        return view('user.certifications.show', compact('page_title', 'certification'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Certification $certification)
    {
        abort_unless(auth()->user()->role->name == 'user', 403, 'You are not authorized to view this page!');
        abort_unless($certification->user_id == auth()->id(), 403, 'You are not authorized to edit this certification!');

        $page_title = 'Edit Certification';
        $upworkIds = UpworkId::latest()->get();

        return view('user.certifications.edit', compact(
            'page_title',
            'certification',
            'upworkIds'
        ));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(CertificationRequest $request, Certification $certification)
    {
        abort_unless(auth()->user()->role->name == 'user', 403, 'You are not authorized to view this page!');
        abort_unless($certification->user_id == auth()->id(), 403, 'You are not authorized to update this certification!');

        $data = $request->validated();

        // Handle file upload
        if ($request->hasFile('certification_upload')) {
            // Delete old file if exists
            if ($certification->certification_upload && Storage::disk('public')->exists($certification->certification_upload)) {
                Storage::disk('public')->delete($certification->certification_upload);
            }

            $file = $request->file('certification_upload');
            $fileName = time() . '_' . $file->getClientOriginalName();
            $filePath = $file->storeAs('certifications', $fileName, 'public');
            $data['certification_upload'] = $filePath;
        }

        $certification->update($data);

        return redirect()->route('certifications.index')
            ->with('success', 'Certification updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Certification $certification)
    {
        abort_unless(auth()->user()->role->name == 'user', 403, 'You are not authorized to view this page!');
        abort_unless($certification->user_id == auth()->id(), 403, 'You are not authorized to delete this certification!');

        // Delete associated file
        if ($certification->certification_upload && Storage::disk('public')->exists($certification->certification_upload)) {
            Storage::disk('public')->delete($certification->certification_upload);
        }

        $certification->delete();

        return redirect()->route('certifications.index')
            ->with('success', 'Certification deleted successfully.');
    }
}
